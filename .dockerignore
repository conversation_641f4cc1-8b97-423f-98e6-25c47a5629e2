# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Environment variables
!.env
.env
.env.*

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Docker
.dockerignore
docker-compose*.yml

# Tests
tests/
pytest_cache/
.coverage
htmlcov/

# Documentation
docs/
README.md
LICENSE

# Temporary files
.DS_Store
*.tmp
*.bak

# Frontend
frontend/node_modules/
frontend/dist/
frontend/.next/
frontend/.nuxt/
frontend/.output/
frontend/.vercel/
frontend/.vscode/
frontend/.idea/
frontend/.gitignore
frontend/.env
frontend/.env.*
frontend/!.env.example
frontend/README.md
frontend/LICENSE
frontend/docs/
frontend/tests/
frontend/pytest_cache/
frontend/.coverage
frontend/htmlcov/
frontend/.dockerignore
frontend/docker-compose*.yml